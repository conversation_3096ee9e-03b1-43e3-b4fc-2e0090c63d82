[MODE: RESEARCH]

我需要为您提供在Prometheus面板上添加监控数据的查询语法和示例，特别是针对您已部署的监控组件。

根据您的系统现状分析文档，您已经部署了以下监控组件：
1. Node Exporter - 系统监控
2. MySQL Exporter - MySQL数据库监控
3. Redis Exporter - Redis缓存监控
4. Nginx Exporter - Nginx服务监控

让我为您提供一些常用的Prometheus查询语法和示例，以便您在Prometheus面板上添加这些监控数据。

[MODE: PLAN]

我将为您提供以下几类监控查询语法和示例：

1. 系统监控查询 (Node Exporter)
   - CPU使用率
   - 内存使用情况
   - 磁盘使用情况
   - 网络流量

2. MySQL监控查询 (MySQL Exporter)
   - 连接数
   - 查询性能
   - 慢查询
   - InnoDB指标

3. Redis监控查询 (Redis Exporter)
   - 内存使用
   - 命令执行
   - 连接情况
   - 键空间统计

4. Nginx监控查询 (Nginx Exporter)
   - 请求率
   - 错误率
   - 连接数

对于每个查询，我将提供：
- 查询语法
- 简短说明
- 使用场景

[MODE: EXECUTE]

以下是您可以在Prometheus面板上添加的监控数据查询语法和说明：

## 1. 系统监控查询 (Node Exporter)

### CPU使用率
```
100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
```
**说明**: 显示CPU使用率百分比，计算方式为100减去空闲CPU时间百分比。
**场景**: 监控服务器CPU负载，识别性能瓶颈。

### 内存使用率
```
100 * (1 - ((node_memory_MemFree_bytes + node_memory_Cached_bytes + node_memory_Buffers_bytes) / node_memory_MemTotal_bytes))
```
**说明**: 计算内存使用率百分比，包括缓存和缓冲区。
**场景**: 监控内存资源使用情况，预防内存不足。

### 磁盘使用率
```
100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"})
```
**说明**: 显示根分区的磁盘使用率百分比。
**场景**: 监控磁盘空间使用情况，防止磁盘空间耗尽。

### 网络流量
```
sum by (instance) (irate(node_network_receive_bytes_total{device!~"lo|veth.*|docker.*|br.*"}[5m]))
```
**说明**: 显示每秒接收的网络流量（不包括本地回环和虚拟接口）。
**场景**: 监控网络带宽使用情况，识别网络瓶颈。

### 系统负载
```
node_load1
```
**说明**: 显示1分钟平均系统负载。
**场景**: 监控系统整体负载情况。

## 2. MySQL监控查询 (MySQL Exporter)

### MySQL连接数
```
mysql_global_status_threads_connected
```
**说明**: 显示当前MySQL活跃连接数。
**场景**: 监控数据库连接使用情况，识别连接泄漏。

### MySQL查询率
```
rate(mysql_global_status_questions[5m])
```
**说明**: 显示每秒执行的查询数。
**场景**: 监控数据库活动水平，识别异常流量。

### MySQL慢查询
```
rate(mysql_global_status_slow_queries[5m])
```
**说明**: 显示每秒执行的慢查询数。
**场景**: 监控数据库性能问题，识别需要优化的查询。

### InnoDB缓冲池使用率
```
mysql_global_status_innodb_buffer_pool_pages_data / mysql_global_status_innodb_buffer_pool_pages_total * 100
```
**说明**: 显示InnoDB缓冲池使用率百分比。
**场景**: 监控数据库缓存效率，优化内存使用。

## 3. Redis监控查询 (Redis Exporter)

### Redis内存使用率
```
redis_memory_used_bytes / redis_memory_max_bytes * 100
```
**说明**: 显示Redis内存使用率百分比。
**场景**: 监控Redis内存使用情况，防止内存溢出。

### Redis命令执行率
```
rate(redis_commands_processed_total[5m])
```
**说明**: 显示每秒处理的Redis命令数。
**场景**: 监控Redis服务器负载，识别高峰期。

### Redis连接数
```
redis_connected_clients
```
**说明**: 显示当前Redis客户端连接数。
**场景**: 监控Redis连接使用情况，识别连接泄漏。

### Redis键过期率
```
rate(redis_expired_keys_total[5m])
```
**说明**: 显示每秒过期的键数量。
**场景**: 监控Redis键过期情况，优化缓存策略。

## 4. Nginx监控查询 (Nginx Exporter)

### Nginx请求率
```
rate(nginx_http_requests_total[5m])
```
**说明**: 显示每秒处理的HTTP请求数。
**场景**: 监控Web服务器负载，识别流量峰值。

### Nginx错误率
```
sum(rate(nginx_http_requests_total{status=~"5.."}[5m])) / sum(rate(nginx_http_requests_total[5m])) * 100
```
**说明**: 显示HTTP 5xx错误占总请求的百分比。
**场景**: 监控Web服务器错误情况，识别服务问题。

### Nginx连接数
```
nginx_connections_active
```
**说明**: 显示当前活跃的Nginx连接数。
**场景**: 监控Web服务器连接使用情况，识别连接瓶颈。

## 5. 组合监控查询

### 系统资源总览
```
(100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80 or
(100 * (1 - ((node_memory_MemFree_bytes + node_memory_Cached_bytes + node_memory_Buffers_bytes) / node_memory_MemTotal_bytes))) > 80 or
(100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"})) > 80
```
**说明**: 显示CPU、内存或磁盘使用率超过80%的情况。
**场景**: 综合监控系统资源使用情况，及时发现资源瓶颈。

### 数据库和缓存健康状况
```
mysql_up == 0 or redis_up == 0
```
**说明**: 检测MySQL或Redis服务是否宕机。
**场景**: 监控关键服务可用性，及时发现服务故障。

## 使用提示

1. **时间范围**: 在查询中使用`[5m]`表示过去5分钟的数据，可以根据需要调整为`[1m]`、`[1h]`等。

2. **聚合函数**:
   - `rate()`: 计算每秒速率
   - `irate()`: 计算瞬时速率
   - `avg()`: 计算平均值
   - `sum()`: 计算总和
   - `max()`: 计算最大值
   - `min()`: 计算最小值

3. **过滤标签**: 使用`{label="value"}`格式过滤特定标签，例如`{instance="175.7.0.11:9104"}`。

4. **正则表达式**: 使用`=~`和正则表达式匹配标签值，例如`{status=~"5.."}`匹配所有5xx状态码。

5. **告警阈值**: 在查询后添加比较操作符和阈值，例如`> 80`表示大于80%时告警。

这些查询可以直接在Prometheus的Graph页面中使用，也可以在Grafana中创建仪表盘时使用。在Grafana中，您可以为每个查询创建更丰富的可视化效果，如图表、仪表盘和热力图等。